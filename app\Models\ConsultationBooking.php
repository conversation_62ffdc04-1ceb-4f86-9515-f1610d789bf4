<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ConsultationBooking extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'veterinarian_id',
        'pet_id',
        'appointment_date',
        'appointment_time',
        'consultation_type',
        'symptoms_description',
        'status',
        'notes',
        'prescription',
        'follow_up_date',
        'consultation_fee',
        'payment_status',
    ];

    protected $casts = [
        'appointment_date' => 'date',
        'follow_up_date' => 'date',
        'consultation_fee' => 'decimal:2',
    ];

    /**
     * Get the user who booked the consultation
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the veterinarian for this consultation
     */
    public function veterinarian()
    {
        return $this->belongsTo(Veterinarian::class);
    }

    /**
     * Get the pet for this consultation
     */
    public function pet()
    {
        return $this->belongsTo(Pet::class);
    }

    /**
     * Check if consultation is scheduled
     */
    public function isScheduled(): bool
    {
        return $this->status === 'scheduled';
    }

    /**
     * Check if consultation is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if consultation is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }
}
