<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Pet;
use App\Models\AdoptionApplication;
use App\Models\LostPetReport;
use App\Models\ConsultationBooking;

class DashboardController extends Controller
{
    /**
     * Show user dashboard
     */
    public function userDashboard()
    {
        $user = auth()->user();

        $data = [
            'user' => $user,
            'pets' => $user->pets()->latest()->take(5)->get(),
            'adoptionApplications' => $user->adoptionApplications()->with('pet')->latest()->take(5)->get(),
            'lostPetReports' => $user->lostPetReports()->latest()->take(5)->get(),
            'consultationBookings' => $user->consultationBookings()->with(['pet', 'veterinarian'])->latest()->take(5)->get(),
            'stats' => [
                'total_pets' => $user->pets()->count(),
                'pending_applications' => $user->adoptionApplications()->where('status', 'pending')->count(),
                'active_lost_reports' => $user->lostPetReports()->where('status', 'lost')->count(),
                'upcoming_consultations' => $user->consultationBookings()->where('appointment_date', '>=', now()->toDateString())->where('status', 'scheduled')->count(),
            ]
        ];

        return view('dashboard.user', $data);
    }

    /**
     * Show admin dashboard
     */
    public function adminDashboard()
    {
        $data = [
            'stats' => [
                'total_users' => \App\Models\User::where('role', 'user')->count(),
                'total_pets' => Pet::count(),
                'available_pets' => Pet::where('is_adopted', false)->count(),
                'pending_applications' => AdoptionApplication::where('status', 'pending')->count(),
                'active_lost_reports' => LostPetReport::where('status', 'lost')->count(),
                'scheduled_consultations' => ConsultationBooking::where('status', 'scheduled')->count(),
            ],
            'recentApplications' => AdoptionApplication::with(['user', 'pet'])->latest()->take(10)->get(),
            'recentLostReports' => LostPetReport::with('user')->latest()->take(10)->get(),
            'upcomingConsultations' => ConsultationBooking::with(['user', 'pet', 'veterinarian'])
                ->where('appointment_date', '>=', now()->toDateString())
                ->orderBy('appointment_date')
                ->orderBy('appointment_time')
                ->take(10)->get(),
        ];

        return view('dashboard.admin', $data);
    }
}
