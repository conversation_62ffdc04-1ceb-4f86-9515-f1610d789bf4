@extends('layouts.app')

@section('title', 'User Dashboard')

@section('content')
<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">Welcome back, {{ $user->name }}!</h1>
                    <p class="text-muted">Manage your pets and explore our services</p>
                </div>
                <div class="text-end">
                    <small class="text-muted">{{ now()->format('l, F j, Y') }}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="bi bi-heart-fill fs-1 mb-2"></i>
                    <h3 class="mb-0">{{ $stats['total_pets'] }}</h3>
                    <p class="mb-0">My Pets</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="bi bi-house-heart-fill fs-1 mb-2"></i>
                    <h3 class="mb-0">{{ $stats['pending_applications'] }}</h3>
                    <p class="mb-0">Pending Applications</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="bi bi-search fs-1 mb-2"></i>
                    <h3 class="mb-0">{{ $stats['active_lost_reports'] }}</h3>
                    <p class="mb-0">Active Lost Reports</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="bi bi-calendar-check-fill fs-1 mb-2"></i>
                    <h3 class="mb-0">{{ $stats['upcoming_consultations'] }}</h3>
                    <p class="mb-0">Upcoming Consultations</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('pets.index') }}" class="btn btn-outline-primary w-100">
                                <i class="bi bi-heart"></i><br>
                                <small>Pet Health & Records</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('adoption.index') }}" class="btn btn-outline-success w-100">
                                <i class="bi bi-house-heart"></i><br>
                                <small>Pet Adoption</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('consultation.index') }}" class="btn btn-outline-info w-100">
                                <i class="bi bi-calendar-check"></i><br>
                                <small>Online Consultation</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('lost-found.index') }}" class="btn btn-outline-warning w-100">
                                <i class="bi bi-search"></i><br>
                                <small>Lost & Found</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <!-- My Pets -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">My Pets</h6>
                    <a href="{{ route('pets.index') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    @if($pets->count() > 0)
                        @foreach($pets as $pet)
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-primary rounded-circle p-2 me-3">
                                    <i class="bi bi-heart-fill text-white"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">{{ $pet->name }}</h6>
                                    <small class="text-muted">{{ $pet->species }} • {{ $pet->breed }}</small>
                                </div>
                                <small class="text-muted">{{ $pet->created_at->diffForHumans() }}</small>
                            </div>
                        @endforeach
                    @else
                        <p class="text-muted mb-0">No pets registered yet.</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Recent Applications -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Recent Applications</h6>
                    <a href="{{ route('adoption.index') }}" class="btn btn-sm btn-outline-success">View All</a>
                </div>
                <div class="card-body">
                    @if($adoptionApplications->count() > 0)
                        @foreach($adoptionApplications as $application)
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-success rounded-circle p-2 me-3">
                                    <i class="bi bi-house-heart-fill text-white"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">{{ $application->pet->name }}</h6>
                                    <small class="text-muted">
                                        Status: 
                                        <span class="badge bg-{{ $application->status === 'pending' ? 'warning' : ($application->status === 'approved' ? 'success' : 'danger') }}">
                                            {{ ucfirst($application->status) }}
                                        </span>
                                    </small>
                                </div>
                                <small class="text-muted">{{ $application->created_at->diffForHumans() }}</small>
                            </div>
                        @endforeach
                    @else
                        <p class="text-muted mb-0">No adoption applications yet.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Lost Pet Reports & Consultations -->
    <div class="row">
        <!-- Lost Pet Reports -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Lost Pet Reports</h6>
                    <a href="{{ route('lost-found.index') }}" class="btn btn-sm btn-outline-warning">View All</a>
                </div>
                <div class="card-body">
                    @if($lostPetReports->count() > 0)
                        @foreach($lostPetReports as $report)
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-warning rounded-circle p-2 me-3">
                                    <i class="bi bi-search text-white"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">{{ $report->pet_name }}</h6>
                                    <small class="text-muted">
                                        Status: 
                                        <span class="badge bg-{{ $report->status === 'lost' ? 'danger' : 'success' }}">
                                            {{ ucfirst($report->status) }}
                                        </span>
                                    </small>
                                </div>
                                <small class="text-muted">{{ $report->created_at->diffForHumans() }}</small>
                            </div>
                        @endforeach
                    @else
                        <p class="text-muted mb-0">No lost pet reports.</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Consultation Bookings -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Consultation Bookings</h6>
                    <a href="{{ route('consultation.index') }}" class="btn btn-sm btn-outline-info">View All</a>
                </div>
                <div class="card-body">
                    @if($consultationBookings->count() > 0)
                        @foreach($consultationBookings as $booking)
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-info rounded-circle p-2 me-3">
                                    <i class="bi bi-calendar-check-fill text-white"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">{{ $booking->pet->name }}</h6>
                                    <small class="text-muted">
                                        Dr. {{ $booking->veterinarian->name }} • 
                                        {{ $booking->appointment_date->format('M j') }}
                                    </small>
                                </div>
                                <small class="text-muted">{{ $booking->created_at->diffForHumans() }}</small>
                            </div>
                        @endforeach
                    @else
                        <p class="text-muted mb-0">No consultation bookings yet.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
