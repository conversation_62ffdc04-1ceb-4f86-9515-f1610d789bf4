<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'PawPortal') }} - @yield('title', 'Pet Care Platform')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: box-shadow 0.15s ease-in-out;
        }
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .navbar-brand {
            font-weight: 600;
            color: #667eea !important;
        }
    </style>

    @stack('styles')
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            @auth
                <!-- Sidebar -->
                <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                    <div class="position-sticky pt-3">
                        <div class="text-center mb-4">
                            <h4 class="text-white">
                                <i class="bi bi-heart-fill"></i> PawPortal
                            </h4>
                            <small class="text-white-50">
                                {{ auth()->user()->isAdmin() ? 'Admin Panel' : 'User Dashboard' }}
                            </small>
                        </div>

                        <ul class="nav flex-column">
                            @if(auth()->user()->isAdmin())
                                <!-- Admin Navigation -->
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" 
                                       href="{{ route('admin.dashboard') }}">
                                        <i class="bi bi-speedometer2"></i> Dashboard
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}" 
                                       href="{{ route('admin.users.index') }}">
                                        <i class="bi bi-people"></i> Users
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('admin.pets.*') ? 'active' : '' }}" 
                                       href="{{ route('admin.pets.index') }}">
                                        <i class="bi bi-heart"></i> Pet Management
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('admin.adoption.*') ? 'active' : '' }}" 
                                       href="{{ route('admin.adoption.applications') }}">
                                        <i class="bi bi-house-heart"></i> Adoption Applications
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('admin.consultation.*') ? 'active' : '' }}" 
                                       href="{{ route('admin.consultation.bookings') }}">
                                        <i class="bi bi-calendar-check"></i> Consultations
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('admin.lost-found.*') ? 'active' : '' }}" 
                                       href="{{ route('admin.lost-found.reports') }}">
                                        <i class="bi bi-search"></i> Lost & Found
                                    </a>
                                </li>
                            @else
                                <!-- User Navigation -->
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('user.dashboard') ? 'active' : '' }}" 
                                       href="{{ route('user.dashboard') }}">
                                        <i class="bi bi-speedometer2"></i> Dashboard
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('pets.*') ? 'active' : '' }}" 
                                       href="{{ route('pets.index') }}">
                                        <i class="bi bi-heart"></i> My Pets
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('adoption.*') ? 'active' : '' }}" 
                                       href="{{ route('adoption.index') }}">
                                        <i class="bi bi-house-heart"></i> Pet Adoption
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('consultation.*') ? 'active' : '' }}" 
                                       href="{{ route('consultation.index') }}">
                                        <i class="bi bi-calendar-check"></i> Online Consultation
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link {{ request()->routeIs('lost-found.*') ? 'active' : '' }}" 
                                       href="{{ route('lost-found.index') }}">
                                        <i class="bi bi-search"></i> Lost & Found
                                    </a>
                                </li>
                            @endif
                        </ul>

                        <hr class="text-white-50">
                        
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <form method="POST" action="{{ route('logout') }}" class="d-inline">
                                    @csrf
                                    <button type="submit" class="nav-link btn btn-link text-start w-100 border-0">
                                        <i class="bi bi-box-arrow-right"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </nav>

                <!-- Main content -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
            @else
                <!-- Guest layout -->
                <main class="col-12">
            @endauth

                <!-- Top Navigation for guests -->
                @guest
                    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm mb-4">
                        <div class="container">
                            <a class="navbar-brand" href="/">
                                <i class="bi bi-heart-fill"></i> PawPortal
                            </a>
                            <div class="navbar-nav ms-auto">
                                <a class="nav-link" href="{{ route('login') }}">Login</a>
                                <a class="nav-link" href="{{ route('register') }}">Register</a>
                            </div>
                        </div>
                    </nav>
                @endguest

                <!-- Content -->
                <div class="py-4">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    @stack('scripts')
</body>
</html>
