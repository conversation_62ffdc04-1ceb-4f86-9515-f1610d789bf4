<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PetHealthRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'pet_id',
        'veterinarian_id',
        'visit_date',
        'visit_type',
        'symptoms',
        'diagnosis',
        'treatment',
        'medications',
        'vaccinations',
        'weight',
        'temperature',
        'notes',
        'next_visit_date',
        'cost',
    ];

    protected $casts = [
        'visit_date' => 'datetime',
        'next_visit_date' => 'date',
        'medications' => 'array',
        'vaccinations' => 'array',
        'cost' => 'decimal:2',
    ];

    /**
     * Get the pet this record belongs to
     */
    public function pet()
    {
        return $this->belongsTo(Pet::class);
    }

    /**
     * Get the veterinarian who created this record
     */
    public function veterinarian()
    {
        return $this->belongsTo(Veterinarian::class);
    }

    /**
     * Check if this is a vaccination record
     */
    public function isVaccinationRecord(): bool
    {
        return $this->visit_type === 'vaccination';
    }

    /**
     * Check if this is a checkup record
     */
    public function isCheckupRecord(): bool
    {
        return $this->visit_type === 'checkup';
    }

    /**
     * Check if this is an emergency record
     */
    public function isEmergencyRecord(): bool
    {
        return $this->visit_type === 'emergency';
    }
}
