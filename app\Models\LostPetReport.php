<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LostPetReport extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'pet_name',
        'species',
        'breed',
        'color',
        'age',
        'gender',
        'description',
        'last_seen_location',
        'last_seen_date',
        'contact_phone',
        'contact_email',
        'reward_amount',
        'image',
        'status',
        'found_date',
        'finder_contact',
    ];

    protected $casts = [
        'last_seen_date' => 'datetime',
        'found_date' => 'datetime',
        'reward_amount' => 'decimal:2',
    ];

    /**
     * Get the user who reported the lost pet
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if pet is still lost
     */
    public function isLost(): bool
    {
        return $this->status === 'lost';
    }

    /**
     * Check if pet has been found
     */
    public function isFound(): bool
    {
        return $this->status === 'found';
    }

    /**
     * Mark pet as found
     */
    public function markAsFound($finderContact = null)
    {
        $this->update([
            'status' => 'found',
            'found_date' => now(),
            'finder_contact' => $finderContact,
        ]);
    }
}
