<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Shelter extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'address',
        'phone',
        'email',
        'website',
        'description',
        'capacity',
        'current_occupancy',
        'license_number',
        'manager_name',
        'operating_hours',
        'services_offered',
    ];

    protected $casts = [
        'services_offered' => 'array',
        'operating_hours' => 'array',
    ];

    /**
     * Get pets in this shelter
     */
    public function pets()
    {
        return $this->hasMany(Pet::class);
    }

    /**
     * Get available pets for adoption
     */
    public function availablePets()
    {
        return $this->pets()->where('is_adopted', false);
    }

    /**
     * Check if shelter has capacity
     */
    public function hasCapacity(): bool
    {
        return $this->current_occupancy < $this->capacity;
    }

    /**
     * Get occupancy percentage
     */
    public function getOccupancyPercentage(): float
    {
        if ($this->capacity == 0) {
            return 0;
        }
        return ($this->current_occupancy / $this->capacity) * 100;
    }
}
