<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;

// Home page
Route::get('/', function () {
    return view('welcome');
});

// Authentication routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
});

Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth')->name('logout');

// User dashboard routes
Route::middleware(['auth', 'user'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'userDashboard'])->name('user.dashboard');

    // Pet Health & Records routes
    Route::prefix('pets')->name('pets.')->group(function () {
        Route::get('/', function () { return view('pets.index'); })->name('index');
        Route::get('/health-records', function () { return view('pets.health-records'); })->name('health-records');
    });

    // Pet Adoption routes
    Route::prefix('adoption')->name('adoption.')->group(function () {
        Route::get('/', function () { return view('adoption.index'); })->name('index');
        Route::get('/apply/{pet}', function () { return view('adoption.apply'); })->name('apply');
    });

    // Online Consultation routes
    Route::prefix('consultation')->name('consultation.')->group(function () {
        Route::get('/', function () { return view('consultation.index'); })->name('index');
        Route::get('/book', function () { return view('consultation.book'); })->name('book');
    });

    // Lost & Found routes
    Route::prefix('lost-found')->name('lost-found.')->group(function () {
        Route::get('/', function () { return view('lost-found.index'); })->name('index');
        Route::get('/report', function () { return view('lost-found.report'); })->name('report');
    });
});

// Admin dashboard routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'adminDashboard'])->name('dashboard');

    // Admin pet management
    Route::prefix('pets')->name('pets.')->group(function () {
        Route::get('/', function () { return view('admin.pets.index'); })->name('index');
        Route::get('/shelters', function () { return view('admin.pets.shelters'); })->name('shelters');
    });

    // Admin adoption management
    Route::prefix('adoption')->name('adoption.')->group(function () {
        Route::get('/applications', function () { return view('admin.adoption.applications'); })->name('applications');
    });

    // Admin consultation management
    Route::prefix('consultation')->name('consultation.')->group(function () {
        Route::get('/bookings', function () { return view('admin.consultation.bookings'); })->name('bookings');
        Route::get('/veterinarians', function () { return view('admin.consultation.veterinarians'); })->name('veterinarians');
    });

    // Admin lost & found management
    Route::prefix('lost-found')->name('lost-found.')->group(function () {
        Route::get('/reports', function () { return view('admin.lost-found.reports'); })->name('reports');
    });

    // User management
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', function () { return view('admin.users.index'); })->name('index');
    });
});
