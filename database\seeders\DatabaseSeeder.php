<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'phone' => '+1234567890',
            'address' => '123 Admin Street, Admin City',
        ]);

        // Create regular user
        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'user',
            'phone' => '+1987654321',
            'address' => '456 User Avenue, User Town',
        ]);

        // Create additional sample users
        User::factory(8)->create([
            'role' => 'user',
        ]);

        // Create sample shelters
        \App\Models\Shelter::create([
            'name' => 'Happy Paws Shelter',
            'address' => '789 Shelter Lane, Pet City, PC 12345',
            'phone' => '+**********',
            'email' => '<EMAIL>',
            'website' => 'https://happypaws.com',
            'description' => 'A loving shelter dedicated to finding homes for abandoned pets.',
            'capacity' => 50,
            'current_occupancy' => 35,
            'license_number' => 'SH-2024-001',
            'manager_name' => 'Sarah Johnson',
            'operating_hours' => json_encode([
                'monday' => '9:00 AM - 6:00 PM',
                'tuesday' => '9:00 AM - 6:00 PM',
                'wednesday' => '9:00 AM - 6:00 PM',
                'thursday' => '9:00 AM - 6:00 PM',
                'friday' => '9:00 AM - 6:00 PM',
                'saturday' => '10:00 AM - 4:00 PM',
                'sunday' => 'Closed'
            ]),
            'services_offered' => json_encode(['Adoption', 'Veterinary Care', 'Grooming', 'Training'])
        ]);

        // Create sample veterinarians
        \App\Models\Veterinarian::create([
            'name' => 'Dr. Emily Wilson',
            'email' => '<EMAIL>',
            'phone' => '+**********',
            'license_number' => 'VET-2024-001',
            'specialization' => 'General Practice',
            'experience_years' => 8,
            'clinic_name' => 'Pet Care Clinic',
            'clinic_address' => '321 Vet Street, Pet City, PC 12345',
            'consultation_fee' => 75.00,
            'available_hours' => json_encode([
                'monday' => '8:00 AM - 5:00 PM',
                'tuesday' => '8:00 AM - 5:00 PM',
                'wednesday' => '8:00 AM - 5:00 PM',
                'thursday' => '8:00 AM - 5:00 PM',
                'friday' => '8:00 AM - 3:00 PM',
                'saturday' => '9:00 AM - 1:00 PM',
                'sunday' => 'Emergency Only'
            ]),
            'bio' => 'Dr. Wilson has been caring for pets for over 8 years and specializes in general practice with a focus on preventive care.',
            'is_active' => true,
        ]);

        \App\Models\Veterinarian::create([
            'name' => 'Dr. Michael Chen',
            'email' => '<EMAIL>',
            'phone' => '+**********',
            'license_number' => 'VET-2024-002',
            'specialization' => 'Surgery',
            'experience_years' => 12,
            'clinic_name' => 'Animal Health Center',
            'clinic_address' => '654 Health Blvd, Pet City, PC 12345',
            'consultation_fee' => 100.00,
            'available_hours' => json_encode([
                'monday' => '7:00 AM - 4:00 PM',
                'tuesday' => '7:00 AM - 4:00 PM',
                'wednesday' => '7:00 AM - 4:00 PM',
                'thursday' => '7:00 AM - 4:00 PM',
                'friday' => '7:00 AM - 2:00 PM',
                'saturday' => 'Emergency Only',
                'sunday' => 'Emergency Only'
            ]),
            'bio' => 'Dr. Chen is a skilled surgeon with 12 years of experience in veterinary medicine, specializing in complex surgical procedures.',
            'is_active' => true,
        ]);
    }
}
