<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Pet extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'species',
        'breed',
        'age',
        'gender',
        'color',
        'weight',
        'description',
        'image',
        'microchip_id',
        'vaccination_status',
        'medical_notes',
        'is_adopted',
        'adoption_fee',
        'shelter_id',
    ];

    protected $casts = [
        'is_adopted' => 'boolean',
        'vaccination_status' => 'array',
    ];

    /**
     * Get the owner of the pet
     */
    public function owner()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the shelter that has this pet
     */
    public function shelter()
    {
        return $this->belongsTo(Shelter::class);
    }

    /**
     * Get pet's health records
     */
    public function healthRecords()
    {
        return $this->hasMany(PetHealthRecord::class);
    }

    /**
     * Get adoption applications for this pet
     */
    public function adoptionApplications()
    {
        return $this->hasMany(AdoptionApplication::class);
    }

    /**
     * Check if pet is available for adoption
     */
    public function isAvailableForAdoption(): bool
    {
        return !$this->is_adopted && $this->shelter_id !== null;
    }
}
