<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdoptionApplication extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'pet_id',
        'status',
        'application_date',
        'reason_for_adoption',
        'living_situation',
        'experience_with_pets',
        'other_pets',
        'work_schedule',
        'emergency_contact',
        'admin_notes',
        'approved_at',
        'rejected_at',
    ];

    protected $casts = [
        'application_date' => 'datetime',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    /**
     * Get the user who submitted the application
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the pet being applied for
     */
    public function pet()
    {
        return $this->belongsTo(Pet::class);
    }

    /**
     * Check if application is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if application is approved
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if application is rejected
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }
}
