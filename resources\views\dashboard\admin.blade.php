@extends('layouts.app')

@section('title', 'Admin Dashboard')

@section('content')
<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">Admin Dashboard</h1>
                    <p class="text-muted">Manage the PawPortal platform</p>
                </div>
                <div class="text-end">
                    <small class="text-muted">{{ now()->format('l, F j, Y') }}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-2 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="bi bi-people-fill fs-1 mb-2"></i>
                    <h3 class="mb-0">{{ $stats['total_users'] }}</h3>
                    <p class="mb-0">Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-2 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="bi bi-heart-fill fs-1 mb-2"></i>
                    <h3 class="mb-0">{{ $stats['total_pets'] }}</h3>
                    <p class="mb-0">Total Pets</p>
                </div>
            </div>
        </div>
        <div class="col-md-2 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="bi bi-house-heart-fill fs-1 mb-2"></i>
                    <h3 class="mb-0">{{ $stats['available_pets'] }}</h3>
                    <p class="mb-0">Available for Adoption</p>
                </div>
            </div>
        </div>
        <div class="col-md-2 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="bi bi-file-earmark-text-fill fs-1 mb-2"></i>
                    <h3 class="mb-0">{{ $stats['pending_applications'] }}</h3>
                    <p class="mb-0">Pending Applications</p>
                </div>
            </div>
        </div>
        <div class="col-md-2 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="bi bi-search fs-1 mb-2"></i>
                    <h3 class="mb-0">{{ $stats['active_lost_reports'] }}</h3>
                    <p class="mb-0">Lost Pet Reports</p>
                </div>
            </div>
        </div>
        <div class="col-md-2 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="bi bi-calendar-check-fill fs-1 mb-2"></i>
                    <h3 class="mb-0">{{ $stats['scheduled_consultations'] }}</h3>
                    <p class="mb-0">Scheduled Consultations</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 mb-3">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary w-100">
                                <i class="bi bi-people"></i><br>
                                <small>Manage Users</small>
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ route('admin.pets.index') }}" class="btn btn-outline-success w-100">
                                <i class="bi bi-heart"></i><br>
                                <small>Manage Pets</small>
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ route('admin.pets.shelters') }}" class="btn btn-outline-info w-100">
                                <i class="bi bi-house"></i><br>
                                <small>Manage Shelters</small>
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ route('admin.adoption.applications') }}" class="btn btn-outline-warning w-100">
                                <i class="bi bi-file-earmark-text"></i><br>
                                <small>Applications</small>
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ route('admin.consultation.bookings') }}" class="btn btn-outline-danger w-100">
                                <i class="bi bi-calendar-check"></i><br>
                                <small>Consultations</small>
                            </a>
                        </div>
                        <div class="col-md-2 mb-3">
                            <a href="{{ route('admin.lost-found.reports') }}" class="btn btn-outline-secondary w-100">
                                <i class="bi bi-search"></i><br>
                                <small>Lost & Found</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <!-- Recent Applications -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Recent Adoption Applications</h6>
                    <a href="{{ route('admin.adoption.applications') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    @if($recentApplications->count() > 0)
                        @foreach($recentApplications->take(5) as $application)
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-primary rounded-circle p-2 me-3">
                                    <i class="bi bi-file-earmark-text-fill text-white"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">{{ $application->user->name }}</h6>
                                    <small class="text-muted">
                                        Applied for {{ $application->pet->name }} • 
                                        <span class="badge bg-{{ $application->status === 'pending' ? 'warning' : ($application->status === 'approved' ? 'success' : 'danger') }}">
                                            {{ ucfirst($application->status) }}
                                        </span>
                                    </small>
                                </div>
                                <small class="text-muted">{{ $application->created_at->diffForHumans() }}</small>
                            </div>
                        @endforeach
                    @else
                        <p class="text-muted mb-0">No recent applications.</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Recent Lost Pet Reports -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Recent Lost Pet Reports</h6>
                    <a href="{{ route('admin.lost-found.reports') }}" class="btn btn-sm btn-outline-warning">View All</a>
                </div>
                <div class="card-body">
                    @if($recentLostReports->count() > 0)
                        @foreach($recentLostReports->take(5) as $report)
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-warning rounded-circle p-2 me-3">
                                    <i class="bi bi-search text-white"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">{{ $report->pet_name }}</h6>
                                    <small class="text-muted">
                                        Reported by {{ $report->user->name }} • 
                                        <span class="badge bg-{{ $report->status === 'lost' ? 'danger' : 'success' }}">
                                            {{ ucfirst($report->status) }}
                                        </span>
                                    </small>
                                </div>
                                <small class="text-muted">{{ $report->created_at->diffForHumans() }}</small>
                            </div>
                        @endforeach
                    @else
                        <p class="text-muted mb-0">No recent lost pet reports.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Consultations -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Upcoming Consultations</h6>
                    <a href="{{ route('admin.consultation.bookings') }}" class="btn btn-sm btn-outline-info">View All</a>
                </div>
                <div class="card-body">
                    @if($upcomingConsultations->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date & Time</th>
                                        <th>Pet Owner</th>
                                        <th>Pet</th>
                                        <th>Veterinarian</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($upcomingConsultations->take(10) as $booking)
                                        <tr>
                                            <td>
                                                <strong>{{ $booking->appointment_date->format('M j, Y') }}</strong><br>
                                                <small class="text-muted">{{ $booking->appointment_time }}</small>
                                            </td>
                                            <td>{{ $booking->user->name }}</td>
                                            <td>{{ $booking->pet->name }}</td>
                                            <td>Dr. {{ $booking->veterinarian->name }}</td>
                                            <td>{{ ucfirst($booking->consultation_type) }}</td>
                                            <td>
                                                <span class="badge bg-{{ $booking->status === 'scheduled' ? 'primary' : ($booking->status === 'completed' ? 'success' : 'secondary') }}">
                                                    {{ ucfirst($booking->status) }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted mb-0">No upcoming consultations.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
