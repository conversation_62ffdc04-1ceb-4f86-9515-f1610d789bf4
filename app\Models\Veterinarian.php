<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Veterinarian extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'license_number',
        'specialization',
        'experience_years',
        'clinic_name',
        'clinic_address',
        'consultation_fee',
        'available_hours',
        'bio',
        'profile_image',
        'is_active',
    ];

    protected $casts = [
        'available_hours' => 'array',
        'consultation_fee' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get consultation bookings for this veterinarian
     */
    public function consultationBookings()
    {
        return $this->hasMany(ConsultationBooking::class);
    }

    /**
     * Get health records created by this veterinarian
     */
    public function healthRecords()
    {
        return $this->hasMany(PetHealthRecord::class);
    }

    /**
     * Check if veterinarian is available
     */
    public function isAvailable(): bool
    {
        return $this->is_active;
    }

    /**
     * Get upcoming consultations
     */
    public function upcomingConsultations()
    {
        return $this->consultationBookings()
            ->where('appointment_date', '>=', now()->toDateString())
            ->where('status', 'scheduled')
            ->orderBy('appointment_date')
            ->orderBy('appointment_time');
    }
}
