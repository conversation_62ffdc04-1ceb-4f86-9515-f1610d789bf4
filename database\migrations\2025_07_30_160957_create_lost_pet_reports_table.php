<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lost_pet_reports', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('pet_name');
            $table->string('species');
            $table->string('breed')->nullable();
            $table->string('color')->nullable();
            $table->integer('age')->nullable();
            $table->enum('gender', ['male', 'female', 'unknown'])->default('unknown');
            $table->text('description')->nullable();
            $table->text('last_seen_location');
            $table->timestamp('last_seen_date');
            $table->string('contact_phone');
            $table->string('contact_email');
            $table->decimal('reward_amount', 8, 2)->nullable();
            $table->string('image')->nullable();
            $table->enum('status', ['lost', 'found'])->default('lost');
            $table->timestamp('found_date')->nullable();
            $table->string('finder_contact')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lost_pet_reports');
    }
};
