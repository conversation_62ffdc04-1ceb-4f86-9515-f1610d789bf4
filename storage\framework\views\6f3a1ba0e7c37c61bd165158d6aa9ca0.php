<?php $__env->startSection('title', 'Welcome to PawPortal'); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Hero Section -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold text-primary mb-3">
                <i class="bi bi-heart-fill"></i> Welcome to PawPortal
            </h1>
            <p class="lead text-muted mb-4">
                Your comprehensive pet care platform - connecting pet owners, shelters, and veterinarians
            </p>
            <div class="d-flex justify-content-center gap-3">
                <a href="<?php echo e(route('login')); ?>" class="btn btn-primary btn-lg">
                    <i class="bi bi-box-arrow-in-right"></i> Sign In
                </a>
                <a href="<?php echo e(route('register')); ?>" class="btn btn-outline-primary btn-lg">
                    <i class="bi bi-person-plus"></i> Get Started
                </a>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="text-center mb-5">Our Services</h2>
        </div>
    </div>

    <div class="row g-4 mb-5">
        <!-- Pet Health & Records -->
        <div class="col-md-6 col-lg-3">
            <div class="card h-100 text-center">
                <div class="card-body">
                    <div class="bg-primary rounded-circle p-3 d-inline-flex mb-3">
                        <i class="bi bi-heart-fill text-white fs-2"></i>
                    </div>
                    <h5 class="card-title">Pet Health & Records</h5>
                    <p class="card-text">
                        Keep track of your pet's health records, vaccinations, and medical history in one secure place.
                    </p>
                </div>
            </div>
        </div>

        <!-- Pet Adoption & Shelter Portal -->
        <div class="col-md-6 col-lg-3">
            <div class="card h-100 text-center">
                <div class="card-body">
                    <div class="bg-success rounded-circle p-3 d-inline-flex mb-3">
                        <i class="bi bi-house-heart-fill text-white fs-2"></i>
                    </div>
                    <h5 class="card-title">Pet Adoption & Shelter Portal</h5>
                    <p class="card-text">
                        Find your perfect companion through our network of trusted shelters and adoption centers.
                    </p>
                </div>
            </div>
        </div>

        <!-- Pet Connect & Online Consultation -->
        <div class="col-md-6 col-lg-3">
            <div class="card h-100 text-center">
                <div class="card-body">
                    <div class="bg-info rounded-circle p-3 d-inline-flex mb-3">
                        <i class="bi bi-calendar-check-fill text-white fs-2"></i>
                    </div>
                    <h5 class="card-title">Pet Connect & Online Consultation</h5>
                    <p class="card-text">
                        Connect with certified veterinarians for online consultations and expert pet care advice.
                    </p>
                </div>
            </div>
        </div>

        <!-- Lost & Found Pet Community Board -->
        <div class="col-md-6 col-lg-3">
            <div class="card h-100 text-center">
                <div class="card-body">
                    <div class="bg-warning rounded-circle p-3 d-inline-flex mb-3">
                        <i class="bi bi-search text-white fs-2"></i>
                    </div>
                    <h5 class="card-title">Lost & Found Pet Community Board</h5>
                    <p class="card-text">
                        Help reunite lost pets with their families through our community-driven platform.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Multi-Pet Dashboard -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body text-center py-5">
                    <div class="bg-secondary rounded-circle p-3 d-inline-flex mb-3">
                        <i class="bi bi-speedometer2 text-white fs-2"></i>
                    </div>
                    <h3 class="card-title">Multi-Pet Dashboard</h3>
                    <p class="card-text lead">
                        Manage all your pets from one comprehensive dashboard. Track health records, 
                        schedule appointments, and stay organized with our intuitive interface.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="row">
        <div class="col-12 text-center">
            <div class="bg-primary text-white rounded p-5">
                <h3 class="mb-3">Ready to Get Started?</h3>
                <p class="mb-4">
                    Join thousands of pet owners who trust PawPortal for their pet care needs.
                </p>
                <a href="<?php echo e(route('register')); ?>" class="btn btn-light btn-lg">
                    <i class="bi bi-person-plus"></i> Create Your Account Today
                </a>
            </div>
        </div>
    </div>

    <!-- Demo Account Information -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="alert alert-info">
                <h5><i class="bi bi-info-circle"></i> Demo Accounts</h5>
                <div class="row">
                    <div class="col-md-6">
                        <strong>Admin Account:</strong><br>
                        Email: <EMAIL><br>
                        Password: password
                    </div>
                    <div class="col-md-6">
                        <strong>User Account:</strong><br>
                        Email: <EMAIL><br>
                        Password: password
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\pawportal\resources\views/welcome.blade.php ENDPATH**/ ?>