<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('shelter_id')->nullable()->constrained()->onDelete('set null');
            $table->string('name');
            $table->string('species');
            $table->string('breed')->nullable();
            $table->integer('age')->nullable();
            $table->enum('gender', ['male', 'female', 'unknown'])->default('unknown');
            $table->string('color')->nullable();
            $table->decimal('weight', 5, 2)->nullable();
            $table->text('description')->nullable();
            $table->string('image')->nullable();
            $table->string('microchip_id')->nullable()->unique();
            $table->json('vaccination_status')->nullable();
            $table->text('medical_notes')->nullable();
            $table->boolean('is_adopted')->default(false);
            $table->decimal('adoption_fee', 8, 2)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pets');
    }
};
